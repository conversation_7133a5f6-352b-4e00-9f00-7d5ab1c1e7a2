<?php

namespace App\Livewire\Dashboard\Reports;

use App\Models\CampaignUtmResult;
use App\Models\UtmConvertion;
use Carbon\Carbon;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class UtmReports extends Component
{
    use WithPagination;

    public $u_trace = null;
    protected $queryString = ['u_trace'];

    public array $data = [
        // 'u_trace' => null,
        'phone' => null,
        'page' => null,
        'utm_source' => null,
        'utm_medium' => null,
        'utm_campaign' => null,
        // 'data_start' => null,
        // 'date_end' => null,
    ];

    #[Url()]
    public $dateStart;

    #[Url()]
    public $dateEnd;

    // برای مدیریت فیلتر چارت
    public $selectedUtmSource = null;
    public $chartData = [];
    public $utmSourceStats = [];

    public function placeholder()
    {
        return <<<'HTML'
        <div
            class="flex gap-3 rounded-md bg-white p-5 h-96 min-h-96 "
                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
                </div>
        HTML;
    }

    public function fillter()
    {
    }

    public function ClearFillter()
    {
        $this->data = [
            'phone' => null,
            'page' => null,
            'utm_source' => null,
            'utm_medium' => null,
            'utm_campaign' => null,
        ];
        $this->selectedUtmSource = null;
        $today = Verta::today()->format('Y/m/d');
        $this->dateStart = $today;
        $this->dateEnd = $today;
    }

    public function selectUtmSource($utmSource)
    {
        $this->selectedUtmSource = $utmSource;
        $this->data['utm_source'] = $utmSource;
    }

    public function selectUtmCampaign($utmCampaign)
    {
        $this->data['utm_campaign'] = $utmCampaign;
    }

    public function clearUtmSourceFilter()
    {
        $this->selectedUtmSource = null;
        $this->data['utm_source'] = null;
        $this->data['utm_campaign'] = null;
    }

    public function mount()
    {
        $today = Verta::today()->format('Y/m/d');
        $this->dateStart = $this->dateStart ?? $today;
        $this->dateEnd = $this->dateEnd ?? $today;
    }

    public function render()
    {
        $reportsQuery = UtmConvertion::latest();

        if (!empty($this->dateStart)) {
            $startDateTime = Verta::parse($this->dateStart)->toCarbon()->startOfDay();

            $endDateTime = !empty($this->dateEnd)
                ? Verta::parse($this->dateEnd)->toCarbon()->endOfDay()
                : Carbon::now()->endOfDay();

            $reportsQuery->where(function ($query) use ($startDateTime, $endDateTime) {
                $query->whereBetween('created_at', [$startDateTime, $endDateTime])
                    ->orWhereBetween('createdAt', [$startDateTime, $endDateTime]);
            });
        }

        if (!empty($this->u_trace)) {
            $reportsQuery->where('u_trace', $this->u_trace);
        }

        if (!empty($this->data['phone'])) {
            $reportsQuery->where('phone', 'like', '%' . $this->data['phone'] . '%');
        }

        if (!empty($this->data['utm_source'])) {
            $reportsQuery->where('cookie_value', 'like', '%utm_source=' . $this->data['utm_source'] . '%');
        }

        if (!empty($this->data['utm_campaign'])) {
            $reportsQuery->where('cookie_value', 'like', '%utm_campaign=' . $this->data['utm_campaign'] . '%');
        }

        $reports = $reportsQuery->paginate(50)->withQueryString();

        $reports->getCollection()->transform(function ($item) {
            $raw = (string) $item->cookie_value;
            parse_str($raw, $parsed);
            $item->cookie_data = $parsed;
            return $item;
        });

        // تولید داده‌های چارت
        $this->generateChartData();

        return view('livewire.dashboard.reports.utm-reports', [
            'reports' => $reports,
            'campaignResults' => CampaignUtmResult::latest()->get(),
            'chartData' => $this->chartData,
            'utmSourceStats' => $this->utmSourceStats
        ]);
    }

    private function generateChartData()
    {
        $query = UtmConvertion::query();

        // اعمال فیلتر تاریخ
        if (!empty($this->dateStart)) {
            $startDateTime = Verta::parse($this->dateStart)->toCarbon()->startOfDay();
            $endDateTime = !empty($this->dateEnd)
                ? Verta::parse($this->dateEnd)->toCarbon()->endOfDay()
                : Carbon::now()->endOfDay();

            $query->where(function ($q) use ($startDateTime, $endDateTime) {
                $q->whereBetween('created_at', [$startDateTime, $endDateTime])
                    ->orWhereBetween('createdAt', [$startDateTime, $endDateTime]);
            });
        }

        $utmData = $query->get();
        $utmSourceStats = [];

        foreach ($utmData as $item) {
            $raw = (string) $item->cookie_value;
            parse_str($raw, $parsed);

            $utmSource = $parsed['utm_source'] ?? 'نامشخص';
            $utmCampaign = $parsed['utm_campaign'] ?? 'نامشخص';

            // آمار utm_source
            if (!isset($utmSourceStats[$utmSource])) {
                $utmSourceStats[$utmSource] = [
                    'count' => 0,
                    'campaigns' => []
                ];
            }
            $utmSourceStats[$utmSource]['count']++;

            // آمار utm_campaign برای هر utm_source
            if (!isset($utmSourceStats[$utmSource]['campaigns'][$utmCampaign])) {
                $utmSourceStats[$utmSource]['campaigns'][$utmCampaign] = 0;
            }
            $utmSourceStats[$utmSource]['campaigns'][$utmCampaign]++;
        }

        // مرتب‌سازی بر اساس تعداد
        uasort($utmSourceStats, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });

        $this->utmSourceStats = $utmSourceStats;

        // تولید داده‌های چارت
        $labels = array_keys($utmSourceStats);
        $data = array_values(array_map(function ($stat) {
            return $stat['count'];
        }, $utmSourceStats));

        $this->chartData = [
            'labels' => $labels,
            'data' => $data
        ];
    }
}
